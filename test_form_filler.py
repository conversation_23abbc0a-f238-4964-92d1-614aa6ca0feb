"""
Test script untuk Google Form Auto-Filler
Script untuk testing dan debugging form filler
"""

from google_form_filler import GoogleFormFiller
import json
import time

def test_browser_setup():
    """Test apakah browser setup ber<PERSON><PERSON> dengan baik"""
    print("Testing browser setup...")
    
    try:
        filler = GoogleFormFiller(headless=False)
        print("✅ Browser setup successful!")
        
        # Test buka Google
        filler.driver.get("https://www.google.com")
        time.sleep(2)
        
        title = filler.driver.title
        print(f"✅ Successfully opened Google. Title: {title}")
        
        filler.close()
        return True
        
    except Exception as e:
        print(f"❌ Browser setup failed: {e}")
        return False

def test_form_detection(form_url):
    """Test deteksi elemen form"""
    print(f"Testing form detection for: {form_url}")
    
    try:
        filler = GoogleFormFiller(headless=False)
        
        if filler.open_form(form_url):
            print("✅ Form opened successfully!")
            
            # Cari semua input fields
            inputs = filler.driver.find_elements("xpath", "//input")
            print(f"Found {len(inputs)} input elements")
            
            for i, input_elem in enumerate(inputs[:5]):  # Show first 5
                input_type = input_elem.get_attribute("type")
                aria_label = input_elem.get_attribute("aria-label")
                name = input_elem.get_attribute("name")
                print(f"  Input {i+1}: type='{input_type}', aria-label='{aria_label}', name='{name}'")
            
            # Cari radio buttons
            radios = filler.driver.find_elements("xpath", "//div[@role='radio']")
            print(f"Found {len(radios)} radio button elements")
            
            # Cari checkboxes
            checkboxes = filler.driver.find_elements("xpath", "//div[@role='checkbox']")
            print(f"Found {len(checkboxes)} checkbox elements")
            
            # Cari dropdowns
            dropdowns = filler.driver.find_elements("xpath", "//div[@role='listbox']")
            print(f"Found {len(dropdowns)} dropdown elements")
            
            input("Press Enter to close browser...")
            filler.close()
            return True
            
        else:
            print("❌ Failed to open form")
            filler.close()
            return False
            
    except Exception as e:
        print(f"❌ Form detection failed: {e}")
        return False

def generate_selectors_for_form(form_url):
    """Generate selector suggestions for a form"""
    print(f"Generating selectors for: {form_url}")
    
    try:
        filler = GoogleFormFiller(headless=False)
        
        if filler.open_form(form_url):
            selectors = {
                "text_fields": [],
                "radio_buttons": [],
                "checkboxes": [],
                "dropdowns": []
            }
            
            # Text inputs
            inputs = filler.driver.find_elements("xpath", "//input[@type='text' or @type='email' or @type='tel' or @type='url']")
            for input_elem in inputs:
                aria_label = input_elem.get_attribute("aria-label")
                if aria_label:
                    selectors["text_fields"].append({
                        "selector": f"//input[@aria-label='{aria_label}']",
                        "value": f"Your {aria_label.lower()}",
                        "by": "xpath",
                        "description": f"Field for {aria_label}"
                    })
            
            # Textareas
            textareas = filler.driver.find_elements("xpath", "//textarea")
            for textarea in textareas:
                aria_label = textarea.get_attribute("aria-label")
                if aria_label:
                    selectors["text_fields"].append({
                        "selector": f"//textarea[@aria-label='{aria_label}']",
                        "value": f"Your {aria_label.lower()}",
                        "by": "xpath",
                        "description": f"Textarea for {aria_label}"
                    })
            
            # Radio button options
            radio_spans = filler.driver.find_elements("xpath", "//div[@role='radio']/following-sibling::div//span")
            for span in radio_spans[:5]:  # Limit to first 5
                text = span.text.strip()
                if text:
                    selectors["radio_buttons"].append(text)
            
            # Checkbox options
            checkbox_spans = filler.driver.find_elements("xpath", "//div[@role='checkbox']/following-sibling::div//span")
            for span in checkbox_spans[:5]:  # Limit to first 5
                text = span.text.strip()
                if text:
                    selectors["checkboxes"].append(text)
            
            # Save to file
            config = {
                "form_url": form_url,
                **selectors,
                "settings": {
                    "wait_time": 10,
                    "headless": False,
                    "auto_submit": False,
                    "delay_between_fields": 1
                }
            }
            
            filename = "generated_config.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Configuration saved to {filename}")
            print("Edit the file to customize values and selectors")
            
            input("Press Enter to close browser...")
            filler.close()
            return True
            
        else:
            print("❌ Failed to open form")
            filler.close()
            return False
            
    except Exception as e:
        print(f"❌ Selector generation failed: {e}")
        return False

def test_specific_config(config_file):
    """Test specific configuration file"""
    print(f"Testing configuration: {config_file}")
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        filler = GoogleFormFiller(headless=False)
        
        if filler.open_form(config['form_url']):
            print("✅ Form opened successfully!")
            
            # Test each field type
            success_count = 0
            total_count = 0
            
            # Test text fields
            for field in config.get('text_fields', []):
                total_count += 1
                if filler.fill_text_field(field['selector'], field['value'], field.get('by', 'xpath')):
                    success_count += 1
                    print(f"✅ Text field filled: {field.get('description', 'Unknown')}")
                else:
                    print(f"❌ Failed to fill text field: {field.get('description', 'Unknown')}")
                time.sleep(1)
            
            # Test radio buttons
            for option in config.get('radio_buttons', []):
                total_count += 1
                if filler.select_radio_button(option):
                    success_count += 1
                    print(f"✅ Radio button selected: {option}")
                else:
                    print(f"❌ Failed to select radio button: {option}")
                time.sleep(1)
            
            # Test checkboxes
            for option in config.get('checkboxes', []):
                total_count += 1
                if filler.select_checkbox(option):
                    success_count += 1
                    print(f"✅ Checkbox selected: {option}")
                else:
                    print(f"❌ Failed to select checkbox: {option}")
                time.sleep(1)
            
            print(f"\nTest Results: {success_count}/{total_count} fields successful")
            
            if success_count == total_count:
                print("🎉 All tests passed!")
            else:
                print("⚠️  Some tests failed. Check selectors and form structure.")
            
            input("Press Enter to close browser...")
            filler.close()
            return success_count == total_count
            
        else:
            print("❌ Failed to open form")
            filler.close()
            return False
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Main testing function"""
    print("🧪 Google Form Auto-Filler Test Suite")
    print("=" * 40)
    
    while True:
        print("\nSelect test option:")
        print("1. Test browser setup")
        print("2. Test form detection")
        print("3. Generate selectors for form")
        print("4. Test specific configuration")
        print("5. Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == "1":
            test_browser_setup()
            
        elif choice == "2":
            form_url = input("Enter form URL: ").strip()
            if form_url:
                test_form_detection(form_url)
            else:
                print("❌ Please enter a valid URL")
                
        elif choice == "3":
            form_url = input("Enter form URL: ").strip()
            if form_url:
                generate_selectors_for_form(form_url)
            else:
                print("❌ Please enter a valid URL")
                
        elif choice == "4":
            config_file = input("Enter config file path: ").strip()
            if config_file:
                test_specific_config(config_file)
            else:
                print("❌ Please enter a valid file path")
                
        elif choice == "5":
            print("Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == "__main__":
    main()
