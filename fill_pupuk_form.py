"""
Script khusus untuk mengisi form pupuk bersubsidi
"""

from google_form_filler import GoogleFormFiller
import json
import time

def fill_pupuk_form_single():
    """Mengisi form pupuk bersubsidi sekali"""
    
    # Data untuk form pupuk bersubsidi
    form_data = {
        "text_fields": [
            {
                "selector": "//input[@aria-label='Email']",
                "value": "<EMAIL>",
                "by": "xpath",
                "description": "Field untuk email"
            },
            {
                "selector": "//input[@aria-label='Nama']",
                "value": "<PERSON><PERSON> Petani",
                "by": "xpath",
                "description": "Field untuk nama"
            }
        ],
        "radio_buttons": [
            "Ya"  # Pilih "Ya" untuk pertanyaan pupuk bersubsidi
        ]
    }
    
    form_url = "https://docs.google.com/forms/d/e/1FAIpQLSd9j_kTyudDMK9UrUz4z9yidOm_TATIrl7_L6KR6vmgKEhMTQ/viewform?usp=dialog"
    
    print("🌾 Mengisi Form Pupuk Bersubsidi")
    print("=" * 40)
    
    filler = GoogleFormFiller(headless=False)
    
    try:
        print("📝 Membuka form...")
        if filler.open_form(form_url):
            print("✅ Form berhasil dibuka!")
            
            print("✏️  Mengisi data...")
            if filler.fill_form_from_data(form_data):
                print("✅ Form berhasil diisi!")
                
                # Tanya apakah ingin submit
                submit = input("\n🤔 Submit form sekarang? (y/n): ").lower().strip()
                if submit == 'y':
                    print("📤 Mengirim form...")
                    if filler.submit_form():
                        print("🎉 Form berhasil dikirim!")
                    else:
                        print("❌ Gagal mengirim form")
                else:
                    print("ℹ️  Form tidak dikirim. Anda bisa submit manual.")
                    input("Tekan Enter untuk menutup browser...")
            else:
                print("❌ Gagal mengisi form!")
        else:
            print("❌ Gagal membuka form!")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        filler.close()

def fill_pupuk_form_multiple():
    """Mengisi form pupuk bersubsidi untuk beberapa orang"""
    
    # Data beberapa petani
    petani_data = [
        {
            "nama": "Budi Santoso",
            "email": "<EMAIL>",
            "pernah_beli": "Ya"
        },
        {
            "nama": "Siti Aminah", 
            "email": "<EMAIL>",
            "pernah_beli": "Tidak"
        },
        {
            "nama": "Ahmad Wijaya",
            "email": "<EMAIL>", 
            "pernah_beli": "Ya"
        }
    ]
    
    form_url = "https://docs.google.com/forms/d/e/1FAIpQLSd9j_kTyudDMK9UrUz4z9yidOm_TATIrl7_L6KR6vmgKEhMTQ/viewform?usp=dialog"
    
    print("🌾 Mengisi Form Pupuk Bersubsidi - Multiple Data")
    print("=" * 50)
    print(f"📊 Total data: {len(petani_data)} petani")
    
    # Konfirmasi
    confirm = input("\n🤔 Lanjutkan mengisi form untuk semua data? (y/n): ").lower().strip()
    if confirm != 'y':
        print("❌ Dibatalkan.")
        return
    
    # Mode headless untuk kecepatan
    auto_submit = input("🤔 Submit otomatis setelah mengisi? (y/n): ").lower().strip() == 'y'
    
    filler = GoogleFormFiller(headless=True)  # Mode headless untuk efisiensi
    
    success_count = 0
    
    try:
        for i, petani in enumerate(petani_data, 1):
            print(f"\n📝 Mengisi data petani {i}/{len(petani_data)}: {petani['nama']}")
            
            # Siapkan data form
            form_data = {
                "text_fields": [
                    {
                        "selector": "//input[@aria-label='Email']",
                        "value": petani["email"],
                        "by": "xpath"
                    },
                    {
                        "selector": "//input[@aria-label='Nama']", 
                        "value": petani["nama"],
                        "by": "xpath"
                    }
                ],
                "radio_buttons": [petani["pernah_beli"]]
            }
            
            # Buka form baru untuk setiap petani
            if filler.open_form(form_url):
                if filler.fill_form_from_data(form_data):
                    if auto_submit:
                        if filler.submit_form():
                            print(f"✅ Data {petani['nama']} berhasil dikirim!")
                            success_count += 1
                        else:
                            print(f"❌ Gagal mengirim data {petani['nama']}")
                    else:
                        print(f"✅ Data {petani['nama']} berhasil diisi!")
                        success_count += 1
                else:
                    print(f"❌ Gagal mengisi data {petani['nama']}")
            else:
                print(f"❌ Gagal membuka form untuk {petani['nama']}")
            
            # Delay antar form
            if i < len(petani_data):
                time.sleep(3)
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        filler.close()
        
        print(f"\n📊 Hasil: {success_count}/{len(petani_data)} data berhasil diproses")
        if success_count == len(petani_data):
            print("🎉 Semua data berhasil diproses!")
        else:
            print("⚠️  Beberapa data gagal diproses.")

def load_from_csv():
    """Load data petani dari file CSV"""
    try:
        import pandas as pd
        
        csv_file = input("📁 Masukkan path file CSV: ").strip()
        if not csv_file:
            print("❌ Path file tidak boleh kosong")
            return
        
        df = pd.read_csv(csv_file)
        print(f"📊 Berhasil load {len(df)} data dari CSV")
        print("Kolom yang tersedia:", list(df.columns))
        
        # Mapping kolom
        nama_col = input("Nama kolom untuk 'Nama': ").strip()
        email_col = input("Nama kolom untuk 'Email': ").strip()
        pupuk_col = input("Nama kolom untuk 'Pernah Beli Pupuk' (Ya/Tidak): ").strip()
        
        form_url = "https://docs.google.com/forms/d/e/1FAIpQLSd9j_kTyudDMK9UrUz4z9yidOm_TATIrl7_L6KR6vmgKEhMTQ/viewform?usp=dialog"
        
        auto_submit = input("🤔 Submit otomatis? (y/n): ").lower().strip() == 'y'
        
        filler = GoogleFormFiller(headless=True)
        success_count = 0
        
        try:
            for index, row in df.iterrows():
                print(f"\n📝 Mengisi data {index+1}/{len(df)}: {row[nama_col]}")
                
                form_data = {
                    "text_fields": [
                        {
                            "selector": "//input[@aria-label='Email']",
                            "value": str(row[email_col]),
                            "by": "xpath"
                        },
                        {
                            "selector": "//input[@aria-label='Nama']",
                            "value": str(row[nama_col]),
                            "by": "xpath"
                        }
                    ],
                    "radio_buttons": [str(row[pupuk_col])]
                }
                
                if filler.open_form(form_url):
                    if filler.fill_form_from_data(form_data):
                        if auto_submit:
                            if filler.submit_form():
                                success_count += 1
                                print(f"✅ Data {row[nama_col]} berhasil dikirim!")
                            else:
                                print(f"❌ Gagal mengirim data {row[nama_col]}")
                        else:
                            success_count += 1
                            print(f"✅ Data {row[nama_col]} berhasil diisi!")
                    else:
                        print(f"❌ Gagal mengisi data {row[nama_col]}")
                else:
                    print(f"❌ Gagal membuka form untuk {row[nama_col]}")
                
                time.sleep(2)  # Delay antar form
        
        finally:
            filler.close()
            print(f"\n📊 Hasil: {success_count}/{len(df)} data berhasil diproses")
    
    except ImportError:
        print("❌ Pandas tidak terinstall. Install dengan: pip install pandas")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function"""
    print("🌾 Form Pupuk Bersubsidi Auto-Filler")
    print("=" * 40)
    
    while True:
        print("\nPilih opsi:")
        print("1. Isi form sekali")
        print("2. Isi form untuk beberapa orang")
        print("3. Load data dari CSV")
        print("4. Test form detection")
        print("5. Keluar")
        
        choice = input("\nPilihan (1-5): ").strip()
        
        if choice == "1":
            fill_pupuk_form_single()
            
        elif choice == "2":
            fill_pupuk_form_multiple()
            
        elif choice == "3":
            load_from_csv()
            
        elif choice == "4":
            # Test form detection
            from test_form_filler import test_form_detection
            form_url = "https://docs.google.com/forms/d/e/1FAIpQLSd9j_kTyudDMK9UrUz4z9yidOm_TATIrl7_L6KR6vmgKEhMTQ/viewform?usp=dialog"
            test_form_detection(form_url)
            
        elif choice == "5":
            print("👋 Selamat tinggal!")
            break
            
        else:
            print("❌ Pilihan tidak valid!")

if __name__ == "__main__":
    main()
