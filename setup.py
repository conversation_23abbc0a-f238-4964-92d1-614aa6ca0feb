"""
Setup script untuk Google Form Auto-Filler
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def install_webdriver_manager():
    """Install webdriver-manager for automatic ChromeDriver management"""
    print("Installing webdriver-manager...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "webdriver-manager"])
        print("✅ Webdriver-manager installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install webdriver-manager: {e}")
        return False

def create_improved_form_filler():
    """Create improved version with webdriver-manager"""
    improved_code = '''"""
Google Form Auto-Filler dengan WebDriver Manager
V<PERSON>i yang lebih mudah digunakan dengan auto-download ChromeDriver
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import time
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GoogleFormFillerAuto:
    def __init__(self, headless=False, wait_time=10):
        """
        Initialize the Google Form Filler with automatic ChromeDriver management
        """
        self.wait_time = wait_time
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver with automatic driver management"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            # Automatically download and setup ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.wait_time)
            logger.info("Chrome WebDriver initialized successfully with auto-managed driver")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            raise
    
    # ... (rest of the methods remain the same as in google_form_filler.py)
'''
    
    try:
        with open('google_form_filler_auto.py', 'w', encoding='utf-8') as f:
            f.write(improved_code)
        print("✅ Created improved form filler with auto ChromeDriver management!")
        return True
    except Exception as e:
        print(f"❌ Failed to create improved form filler: {e}")
        return False

def check_chrome_installation():
    """Check if Chrome is installed"""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ Chrome found at: {path}")
            return True
    
    print("❌ Chrome not found. Please install Google Chrome first.")
    print("Download from: https://www.google.com/chrome/")
    return False

def create_sample_config():
    """Create a sample configuration file"""
    sample_config = {
        "form_url": "https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform",
        "text_fields": [
            {
                "selector": "//input[@aria-label='Name']",
                "value": "Your Name",
                "by": "xpath",
                "description": "Name field"
            },
            {
                "selector": "//input[@type='email']",
                "value": "<EMAIL>",
                "by": "xpath",
                "description": "Email field"
            }
        ],
        "radio_buttons": ["Option 1"],
        "checkboxes": ["Checkbox Option 1"],
        "dropdowns": ["Dropdown Option 1"],
        "settings": {
            "wait_time": 10,
            "headless": False,
            "auto_submit": False,
            "delay_between_fields": 1
        }
    }
    
    try:
        with open('sample_config.json', 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
        print("✅ Sample configuration file created: sample_config.json")
        return True
    except Exception as e:
        print(f"❌ Failed to create sample config: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Google Form Auto-Filler Setup")
    print("=" * 40)
    
    # Check Chrome installation
    if not check_chrome_installation():
        print("\n⚠️  Please install Google Chrome and run setup again.")
        return
    
    # Install requirements
    if not install_requirements():
        print("\n❌ Setup failed. Please check your internet connection and try again.")
        return
    
    # Install webdriver-manager
    if not install_webdriver_manager():
        print("\n❌ Failed to install webdriver-manager. Manual ChromeDriver setup may be required.")
    
    # Create sample config
    create_sample_config()
    
    print("\n" + "=" * 40)
    print("✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit 'sample_config.json' with your form details")
    print("2. Run: python form_filler_gui.py (for GUI)")
    print("3. Or run: python example_usage.py (for examples)")
    print("\nFor help, check README.md")

if __name__ == "__main__":
    main()
