"""
Google Form Auto-Filler dengan GUI
Script dengan interface sederhana untuk mengisi form Google
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import threading
from google_form_filler import GoogleFormFiller
import logging

class FormFillerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Google Form Auto-Filler")
        self.root.geometry("800x600")
        
        self.filler = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Form URL
        ttk.Label(main_frame, text="URL Form Google:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.url_var = tk.StringVar()
        url_entry = ttk.Entry(main_frame, textvariable=self.url_var, width=70)
        url_entry.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Configuration file
        ttk.Label(main_frame, text="File Konfigurasi:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.config_var = tk.StringVar()
        config_entry = ttk.Entry(main_frame, textvariable=self.config_var, width=50)
        config_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Browse", command=self.browse_config).grid(row=1, column=2, pady=5)
        
        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Opsi", padding="5")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        self.headless_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="Jalankan tanpa tampilan browser (headless)", 
                       variable=self.headless_var).grid(row=0, column=0, sticky=tk.W)
        
        self.auto_submit_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="Submit otomatis setelah mengisi", 
                       variable=self.auto_submit_var).grid(row=1, column=0, sticky=tk.W)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        ttk.Button(buttons_frame, text="Mulai Mengisi Form", 
                  command=self.start_filling).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Stop", 
                  command=self.stop_filling).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Buat Template", 
                  command=self.create_template).pack(side=tk.LEFT, padx=5)
        
        # Log area
        ttk.Label(main_frame, text="Log:").grid(row=4, column=0, sticky=tk.W, pady=(10,0))
        self.log_text = scrolledtext.ScrolledText(main_frame, height=15, width=80)
        self.log_text.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # Setup logging to GUI
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging to display in GUI"""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
            
            def emit(self, record):
                msg = self.format(record)
                self.text_widget.insert(tk.END, msg + '\n')
                self.text_widget.see(tk.END)
                self.text_widget.update()
        
        # Add GUI handler to logger
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(gui_handler)
        logging.getLogger().setLevel(logging.INFO)
    
    def browse_config(self):
        """Browse for configuration file"""
        filename = filedialog.askopenfilename(
            title="Pilih file konfigurasi",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.config_var.set(filename)
    
    def create_template(self):
        """Create a template configuration file"""
        filename = filedialog.asksaveasfilename(
            title="Simpan template konfigurasi",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            template = {
                "form_url": self.url_var.get() or "https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform",
                "text_fields": [
                    {
                        "selector": "//input[@aria-label='Nama']",
                        "value": "Nama Anda",
                        "by": "xpath",
                        "description": "Field nama"
                    }
                ],
                "radio_buttons": ["Pilihan 1"],
                "checkboxes": ["Opsi checkbox"],
                "dropdowns": ["Pilihan dropdown"],
                "settings": {
                    "wait_time": 10,
                    "headless": False,
                    "auto_submit": False,
                    "delay_between_fields": 1
                }
            }
            
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(template, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Sukses", f"Template berhasil dibuat: {filename}")
                self.log_text.insert(tk.END, f"Template dibuat: {filename}\n")
            except Exception as e:
                messagebox.showerror("Error", f"Gagal membuat template: {e}")
    
    def start_filling(self):
        """Start filling the form in a separate thread"""
        if not self.url_var.get():
            messagebox.showerror("Error", "Masukkan URL form Google!")
            return
        
        if not self.config_var.get():
            messagebox.showerror("Error", "Pilih file konfigurasi!")
            return
        
        # Start in separate thread to prevent GUI freezing
        thread = threading.Thread(target=self.fill_form_thread)
        thread.daemon = True
        thread.start()
    
    def fill_form_thread(self):
        """Fill form in separate thread"""
        try:
            # Load configuration
            with open(self.config_var.get(), 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Initialize form filler
            self.filler = GoogleFormFiller(headless=self.headless_var.get())
            
            # Open form
            form_url = self.url_var.get() or config.get('form_url')
            if self.filler.open_form(form_url):
                # Fill form
                if self.filler.fill_form_from_data(config):
                    if self.auto_submit_var.get():
                        self.filler.submit_form()
                        self.log_text.insert(tk.END, "Form berhasil disubmit!\n")
                    else:
                        self.log_text.insert(tk.END, "Form berhasil diisi! Silakan submit manual.\n")
                else:
                    self.log_text.insert(tk.END, "Gagal mengisi form!\n")
            else:
                self.log_text.insert(tk.END, "Gagal membuka form!\n")
                
        except Exception as e:
            self.log_text.insert(tk.END, f"Error: {e}\n")
            messagebox.showerror("Error", f"Terjadi kesalahan: {e}")
    
    def stop_filling(self):
        """Stop the form filling process"""
        if self.filler:
            self.filler.close()
            self.filler = None
            self.log_text.insert(tk.END, "Proses dihentikan.\n")

def main():
    root = tk.Tk()
    app = FormFillerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
