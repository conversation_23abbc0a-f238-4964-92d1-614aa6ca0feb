"""
Contoh penggunaan Google Form Auto-Filler
"""

from google_form_filler import GoogleFormFiller
import json

def example_simple_usage():
    """Contoh penggunaan sederhana"""
    
    # Data yang akan diisi ke form
    form_data = {
        "text_fields": [
            {
                "selector": "//input[@aria-label='<PERSON><PERSON>']",
                "value": "John Doe",
                "by": "xpath"
            },
            {
                "selector": "//input[@type='email']",
                "value": "<EMAIL>",
                "by": "xpath"
            },
            {
                "selector": "//textarea[@aria-label='Pesan']",
                "value": "Ini adalah pesan otomatis dari script Python",
                "by": "xpath"
            }
        ],
        "radio_buttons": [
            "Laki-laki"  # Pilih radio button dengan teks "Laki-laki"
        ],
        "checkboxes": [
            "Saya setuju dengan syarat dan ketentuan",
            "<PERSON><PERSON> salinan ke email saya"
        ],
        "dropdowns": [
            "Jakarta"  # Pilih opsi "Jakarta" dari dropdown
        ]
    }
    
    # URL form Google (ganti dengan URL form Anda)
    form_url = "https://docs.google.com/forms/d/e/1FAIpQLSe.../viewform"
    
    # Inisialisasi form filler
    filler = GoogleFormFiller(headless=False)  # Set True untuk mode headless
    
    try:
        print("Membuka form...")
        if filler.open_form(form_url):
            print("Form berhasil dibuka!")
            
            print("Mengisi form...")
            if filler.fill_form_from_data(form_data):
                print("Form berhasil diisi!")
                
                # Uncomment baris berikut untuk submit otomatis
                # print("Mengirim form...")
                # filler.submit_form()
                # print("Form berhasil dikirim!")
                
                input("Tekan Enter untuk menutup browser...")
            else:
                print("Gagal mengisi form!")
        else:
            print("Gagal membuka form!")
    
    finally:
        filler.close()

def example_with_config_file():
    """Contoh penggunaan dengan file konfigurasi"""
    
    # Buat file konfigurasi
    config = {
        "form_url": "https://docs.google.com/forms/d/e/1FAIpQLSe.../viewform",
        "text_fields": [
            {
                "selector": "//input[@aria-label='Nama']",
                "value": "Jane Smith",
                "by": "xpath",
                "description": "Field untuk nama"
            },
            {
                "selector": "//input[@type='email']",
                "value": "<EMAIL>",
                "by": "xpath",
                "description": "Field untuk email"
            }
        ],
        "radio_buttons": ["Perempuan"],
        "checkboxes": ["Newsletter", "Update produk"],
        "dropdowns": ["Bandung"],
        "settings": {
            "wait_time": 15,
            "headless": False,
            "auto_submit": False,
            "delay_between_fields": 2
        }
    }
    
    # Simpan konfigurasi ke file
    with open('my_form_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("File konfigurasi 'my_form_config.json' telah dibuat!")
    print("Edit file tersebut sesuai dengan form Anda, lalu jalankan:")
    print("python example_usage.py --config my_form_config.json")

def fill_multiple_forms():
    """Contoh mengisi beberapa form dengan data berbeda"""
    
    forms_data = [
        {
            "url": "https://docs.google.com/forms/d/e/FORM1/viewform",
            "data": {
                "text_fields": [
                    {"selector": "//input[@aria-label='Nama']", "value": "Person 1", "by": "xpath"}
                ],
                "radio_buttons": ["Option A"]
            }
        },
        {
            "url": "https://docs.google.com/forms/d/e/FORM2/viewform",
            "data": {
                "text_fields": [
                    {"selector": "//input[@aria-label='Nama']", "value": "Person 2", "by": "xpath"}
                ],
                "radio_buttons": ["Option B"]
            }
        }
    ]
    
    filler = GoogleFormFiller(headless=True)  # Mode headless untuk kecepatan
    
    try:
        for i, form_info in enumerate(forms_data, 1):
            print(f"Mengisi form {i}...")
            
            if filler.open_form(form_info["url"]):
                if filler.fill_form_from_data(form_info["data"]):
                    # filler.submit_form()  # Uncomment untuk submit
                    print(f"Form {i} berhasil diisi!")
                else:
                    print(f"Gagal mengisi form {i}")
            else:
                print(f"Gagal membuka form {i}")
    
    finally:
        filler.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-config":
        example_with_config_file()
    elif len(sys.argv) > 2 and sys.argv[1] == "--config":
        # Load dan gunakan file konfigurasi
        config_file = sys.argv[2]
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            filler = GoogleFormFiller(headless=config.get('settings', {}).get('headless', False))
            
            if filler.open_form(config['form_url']):
                if filler.fill_form_from_data(config):
                    if config.get('settings', {}).get('auto_submit', False):
                        filler.submit_form()
                    print("Form berhasil diisi!")
                else:
                    print("Gagal mengisi form!")
            else:
                print("Gagal membuka form!")
            
            filler.close()
            
        except Exception as e:
            print(f"Error: {e}")
    else:
        print("Pilih mode:")
        print("1. Contoh sederhana")
        print("2. Buat file konfigurasi")
        print("3. Isi beberapa form")
        
        choice = input("Pilihan (1-3): ")
        
        if choice == "1":
            example_simple_usage()
        elif choice == "2":
            example_with_config_file()
        elif choice == "3":
            fill_multiple_forms()
        else:
            print("Pilihan tidak valid!")
