"""
Google Form Auto-Filler
Script untuk mengisi form Google secara otomatis menggunakan Selenium WebDriver
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import time
import json
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GoogleFormFiller:
    def __init__(self, headless=False, wait_time=10):
        """
        Initialize the Google Form Filler
        
        Args:
            headless (bool): Run browser in headless mode
            wait_time (int): Maximum wait time for elements
        """
        self.wait_time = wait_time
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Setup Chrome WebDriver with options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            # Try to use webdriver-manager for automatic ChromeDriver management
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                logger.info("Chrome WebDriver initialized with auto-managed driver")
            except Exception:
                # Fallback to system ChromeDriver
                self.driver = webdriver.Chrome(options=chrome_options)
                logger.info("Chrome WebDriver initialized with system driver")

            self.wait = WebDriverWait(self.driver, self.wait_time)
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            logger.error("Make sure Chrome is installed and ChromeDriver is available")
            raise
    
    def open_form(self, form_url):
        """Open Google Form URL"""
        try:
            self.driver.get(form_url)
            logger.info(f"Opened form: {form_url}")
            time.sleep(2)  # Wait for form to load
            return True
        except Exception as e:
            logger.error(f"Failed to open form: {e}")
            return False
    
    def fill_text_field(self, field_selector, value, by_type="xpath"):
        """Fill text input field"""
        try:
            if by_type == "xpath":
                element = self.wait.until(EC.presence_of_element_located((By.XPATH, field_selector)))
            elif by_type == "css":
                element = self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, field_selector)))
            else:
                element = self.wait.until(EC.presence_of_element_located((By.NAME, field_selector)))
            
            element.clear()
            element.send_keys(value)
            logger.info(f"Filled text field with value: {value}")
            return True
        except TimeoutException:
            logger.error(f"Text field not found: {field_selector}")
            return False
    
    def select_radio_button(self, option_text):
        """Select radio button by text"""
        try:
            # Find radio button by text content
            radio_xpath = f"//span[contains(text(), '{option_text}')]/preceding-sibling::div//div[@role='radio']"
            radio_element = self.wait.until(EC.element_to_be_clickable((By.XPATH, radio_xpath)))
            radio_element.click()
            logger.info(f"Selected radio button: {option_text}")
            return True
        except TimeoutException:
            logger.error(f"Radio button not found: {option_text}")
            return False
    
    def select_checkbox(self, option_text):
        """Select checkbox by text"""
        try:
            # Find checkbox by text content
            checkbox_xpath = f"//span[contains(text(), '{option_text}')]/preceding-sibling::div//div[@role='checkbox']"
            checkbox_element = self.wait.until(EC.element_to_be_clickable((By.XPATH, checkbox_xpath)))
            checkbox_element.click()
            logger.info(f"Selected checkbox: {option_text}")
            return True
        except TimeoutException:
            logger.error(f"Checkbox not found: {option_text}")
            return False
    
    def select_dropdown(self, option_text):
        """Select dropdown option by text"""
        try:
            # Click dropdown to open
            dropdown_xpath = "//div[@role='listbox']"
            dropdown = self.wait.until(EC.element_to_be_clickable((By.XPATH, dropdown_xpath)))
            dropdown.click()
            
            # Select option
            option_xpath = f"//span[contains(text(), '{option_text}')]"
            option = self.wait.until(EC.element_to_be_clickable((By.XPATH, option_xpath)))
            option.click()
            logger.info(f"Selected dropdown option: {option_text}")
            return True
        except TimeoutException:
            logger.error(f"Dropdown option not found: {option_text}")
            return False
    
    def fill_form_from_data(self, form_data):
        """
        Fill form using data dictionary
        
        Args:
            form_data (dict): Dictionary containing form field data
                Format: {
                    "text_fields": [{"selector": "xpath", "value": "text", "by": "xpath"}],
                    "radio_buttons": ["Option 1", "Option 2"],
                    "checkboxes": ["Checkbox 1", "Checkbox 2"],
                    "dropdowns": ["Dropdown Option"]
                }
        """
        try:
            # Fill text fields
            if "text_fields" in form_data:
                for field in form_data["text_fields"]:
                    self.fill_text_field(
                        field["selector"], 
                        field["value"], 
                        field.get("by", "xpath")
                    )
                    time.sleep(1)
            
            # Select radio buttons
            if "radio_buttons" in form_data:
                for option in form_data["radio_buttons"]:
                    self.select_radio_button(option)
                    time.sleep(1)
            
            # Select checkboxes
            if "checkboxes" in form_data:
                for option in form_data["checkboxes"]:
                    self.select_checkbox(option)
                    time.sleep(1)
            
            # Select dropdown options
            if "dropdowns" in form_data:
                for option in form_data["dropdowns"]:
                    self.select_dropdown(option)
                    time.sleep(1)
            
            logger.info("Form filled successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error filling form: {e}")
            return False
    
    def submit_form(self):
        """Submit the form"""
        try:
            # Find and click submit button
            submit_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Submit') or contains(text(), 'Kirim')]"))
            )
            submit_button.click()
            logger.info("Form submitted successfully")
            time.sleep(3)  # Wait for submission to complete
            return True
        except TimeoutException:
            logger.error("Submit button not found")
            return False
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def load_form_data(json_file):
    """Load form data from JSON file"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load form data: {e}")
        return None

def main():
    """Main function to demonstrate usage"""
    # Example form data
    sample_data = {
        "text_fields": [
            {
                "selector": "//input[@type='text'][1]",
                "value": "John Doe",
                "by": "xpath"
            },
            {
                "selector": "//input[@type='email']",
                "value": "<EMAIL>",
                "by": "xpath"
            }
        ],
        "radio_buttons": ["Option 1"],
        "checkboxes": ["Checkbox Option 1", "Checkbox Option 2"],
        "dropdowns": ["Dropdown Option 1"]
    }
    
    # Form URL (replace with your actual form URL)
    form_url = "https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform"
    
    # Initialize form filler
    filler = GoogleFormFiller(headless=False)
    
    try:
        # Open form
        if filler.open_form(form_url):
            # Fill form
            if filler.fill_form_from_data(sample_data):
                # Submit form (uncomment to actually submit)
                # filler.submit_form()
                print("Form filled successfully!")
            else:
                print("Failed to fill form")
        else:
            print("Failed to open form")
    
    finally:
        # Close browser
        filler.close()

if __name__ == "__main__":
    main()
