# Google Form Auto-Filler

Script Python untuk mengisi form Google secara otomatis menggunakan Selenium WebDriver.

## Fitur

- ✅ Mengisi text fields, radio buttons, checkboxes, dan dropdowns
- ✅ Support untuk berbagai jenis selector (XPath, CSS, Name)
- ✅ Mode headless untuk menjalankan tanpa tampilan browser
- ✅ GUI sederhana untuk kemudahan penggunaan
- ✅ Konfigurasi melalui file JSON
- ✅ Logging untuk tracking proses
- ✅ Support untuk mengisi multiple forms

## Instalasi

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install ChromeDriver:**
   - Download ChromeDriver dari https://chromedriver.chromium.org/
   - Pastikan ChromeDriver ada di PATH atau letakkan di folder yang sama dengan script
   - Atau gunakan webdriver-manager (sudah termasuk dalam requirements.txt)

## Cara Penggunaan

### 1. Menggunakan GUI (Recommended)

```bash
python form_filler_gui.py
```

1. Masukkan URL form Google
2. Pilih file konfigurasi JSON atau buat template baru
3. Atur opsi sesuai kebutuhan
4. Klik "Mulai Mengisi Form"

### 2. Menggunakan Script Langsung

```python
from google_form_filler import GoogleFormFiller

# Data form
form_data = {
    "text_fields": [
        {
            "selector": "//input[@aria-label='Nama']",
            "value": "John Doe",
            "by": "xpath"
        }
    ],
    "radio_buttons": ["Laki-laki"],
    "checkboxes": ["Setuju dengan syarat"],
    "dropdowns": ["Jakarta"]
}

# Inisialisasi dan jalankan
filler = GoogleFormFiller(headless=False)
filler.open_form("https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform")
filler.fill_form_from_data(form_data)
# filler.submit_form()  # Uncomment untuk submit otomatis
filler.close()
```

### 3. Menggunakan File Konfigurasi

```bash
python example_usage.py --create-config  # Buat template konfigurasi
python example_usage.py --config my_form_config.json  # Gunakan konfigurasi
```

## Format File Konfigurasi

```json
{
  "form_url": "https://docs.google.com/forms/d/e/YOUR_FORM_ID/viewform",
  "text_fields": [
    {
      "selector": "//input[@aria-label='Nama']",
      "value": "John Doe",
      "by": "xpath",
      "description": "Field untuk nama"
    }
  ],
  "radio_buttons": ["Pilihan 1"],
  "checkboxes": ["Checkbox 1", "Checkbox 2"],
  "dropdowns": ["Dropdown Option"],
  "settings": {
    "wait_time": 10,
    "headless": false,
    "auto_submit": false,
    "delay_between_fields": 1
  }
}
```

## Cara Menemukan Selector

### 1. Menggunakan Developer Tools

1. Buka form Google di browser
2. Klik kanan pada field yang ingin diisi → "Inspect Element"
3. Cari atribut yang unik seperti `aria-label`, `name`, atau `id`

### 2. Contoh Selector

```javascript
// Text input dengan aria-label
//input[@aria-label='Nama Lengkap']

// Email input
//input[@type='email']

// Textarea
//textarea[@aria-label='Pesan']

// Radio button berdasarkan teks
//span[contains(text(), 'Laki-laki')]/preceding-sibling::div//div[@role='radio']

// Checkbox berdasarkan teks
//span[contains(text(), 'Setuju')]/preceding-sibling::div//div[@role='checkbox']
```

## Tips Penggunaan

1. **Testing:** Selalu test dengan mode `headless=False` terlebih dahulu
2. **Wait Time:** Sesuaikan `wait_time` jika form lambat loading
3. **Selector:** Gunakan `aria-label` sebagai selector utama karena lebih stabil
4. **Multiple Forms:** Gunakan mode headless untuk mengisi banyak form sekaligus
5. **Error Handling:** Periksa log untuk debugging jika ada error

## Troubleshooting

### ChromeDriver Issues
```bash
# Install webdriver-manager untuk auto-download ChromeDriver
pip install webdriver-manager
```

### Element Not Found
- Periksa selector dengan Developer Tools
- Tambahkan wait time yang lebih lama
- Pastikan form sudah fully loaded

### Permission Issues
- Pastikan Chrome tidak diblokir oleh antivirus
- Jalankan sebagai administrator jika perlu

## Contoh Lengkap

Lihat file `example_usage.py` untuk contoh penggunaan yang lebih detail.

## Disclaimer

Script ini dibuat untuk tujuan otomasi yang sah. Pastikan Anda memiliki izin untuk mengisi form yang ditargetkan dan mematuhi terms of service Google Forms.

## License

MIT License - Silakan gunakan dan modifikasi sesuai kebutuhan.
